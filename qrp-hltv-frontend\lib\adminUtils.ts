// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['creator', 'support'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }

  // Orange wavy gradient glow for creator, green for support
  if (role === 'creator') {
    return 'font-bold transition text-transparent bg-clip-text drop-shadow-[0_0_8px_rgba(251,146,60,0.8)]' +
           ' bg-gradient-to-r from-orange-300 via-yellow-400 via-orange-400 via-red-400 to-orange-300'
  } else if (role === 'support') {
    return 'font-bold transition text-transparent bg-clip-text drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]' +
           ' bg-gradient-to-r from-green-300 via-emerald-300 via-green-400 via-lime-300 to-green-300'
  }

  return baseClasses || 'font-bold text-lg transition'
}
