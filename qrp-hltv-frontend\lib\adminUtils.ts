// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['creator', 'support'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }

  // Orange bright gradient for creator, green bright gradient for support
  if (role === 'creator') {
    return 'font-bold transition text-transparent bg-clip-text drop-shadow-[0_0_12px_rgba(255,165,0,0.9)]' +
           ' bg-gradient-to-r from-orange-400 via-yellow-300 via-orange-500 via-red-500 to-orange-400'
  } else if (role === 'support') {
    return 'font-bold transition text-transparent bg-clip-text drop-shadow-[0_0_12px_rgba(0,255,0,0.9)]' +
           ' bg-gradient-to-r from-green-400 via-emerald-400 via-green-500 via-lime-400 to-green-400'
  }

  return baseClasses || 'font-bold text-lg transition'
}
